import React, { useEffect, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment } from '@react-three/drei';
import { Avatars } from './Avatars';

export default function AvatarsWorld({ rawPhonemeString }) {
  const [phonemeTimeline, setPhonemeTimeline] = useState([]);
  const [currentViseme, setCurrentViseme] = useState('');
  const [expression, setExpression] = useState('neutral');

  // ✅ Convert raw phoneme string to timeline usable by visemeMap
  useEffect(() => {
    if (!rawPhonemeString) return;

    const tokens = rawPhonemeString.trim().split(/\s+/);
    const parsedTimeline = tokens.map((token) => {
      const cleaned = token
        .replace(/[\d\-!']/g, '')
        .replace('-ph', '')
        .toUpperCase();
      return {
        viseme: cleaned || 'neutral',
        expression: 'neutral',
        duration: 100
      };
    });

    setPhonemeTimeline(parsedTimeline);
  }, [rawPhonemeString]);

  // ✅ Playback visemes
  useEffect(() => {
    if (phonemeTimeline.length === 0) return;

    let index = 0;
    let timeout;

    const runNext = () => {
      if (index < phonemeTimeline.length) {
        const { viseme, expression, duration } = phonemeTimeline[index];
        setCurrentViseme(viseme || 'neutral');
        setExpression(expression || 'neutral');
        timeout = setTimeout(runNext, duration || 100);
        index++;
      } else {
        setCurrentViseme('neutral');
      }
    };

    runNext();
    return () => clearTimeout(timeout);
  }, [phonemeTimeline]);

  return (
    <Canvas shadows dpr={[1, 2]} camera={{ position: [1, 0, 1], fov: 20, near: 0.1, far: 100 }}>
      <PerspectiveCamera makeDefault position={[0, 0.1, 1]} fov={20} />
      <directionalLight
        castShadow
        position={[0, 2, 2]}
        intensity={0.6}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <directionalLight position={[2, 6, 5]} intensity={0.2} color="#FDF4DC" />
      <directionalLight position={[2, 6, 5]} intensity={0.3} color="#FDF4DC" />
      <Environment preset="sunset" />
      <mesh receiveShadow rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.2, 0]}>
        <planeGeometry args={[10, 10]} />
        <shadowMaterial transparent opacity={0.2} />
      </mesh>
      <Avatars
        position={[0, -1.37, 0]}
        scale={[1.5, 1.5, 1.5]}
        currentViseme={currentViseme}
        expression={expression}
      />
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={false}
        maxPolarAngle={Math.PI / 2}
      />
    </Canvas>
  );
}
