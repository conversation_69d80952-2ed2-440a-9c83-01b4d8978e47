import React, { useEffect, useRef, useState } from 'react';
import io from 'socket.io-client';
import { X, BarChart3 } from 'lucide-react';
import EngagementDashboard from './EngagementDashboard';

// API Configuration - Aligned with nginx proxy configuration
// /api/content/start_quiz -> http://195.35.21.98:8036
// /api/content/next_question -> http://195.35.21.98:8036
// /socketio2 -> http://195.35.21.98:8036
const FLASK_API_URL = 'https://sasthra.in';
const SOCKETIO_URL = 'https://sasthra.in';
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 5;
const QUESTION_READING_TIME_S = 15;

const LiveQuiz = ({ quizId: inputQuizId, onClose }) => {
  const [quizId, setQuizId] = useState(inputQuizId);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [summary, setSummary] = useState(null);
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [error, setError] = useState(null);
  const [centerCode] = useState(sessionStorage.getItem('centercode') || '');
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);

  // Manual quiz ID input states
  const [showManualInput, setShowManualInput] = useState(!inputQuizId);
  const [manualQuizId, setManualQuizId] = useState('');

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);

  useEffect(() => {
    if (quizId && videoRef.current) {
      initCamera();
    }
  }, [quizId]);

  useEffect(() => {
    return () => {
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  const startQuiz = async () => {
    if (!inputQuizId) {
      setError('Missing Quiz ID');
      return;
    }
    
    try {
      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: inputQuizId }),
      });
      
      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  const handleNextQuestion = async () => {
    if (!quizId) return;
    
    try {
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');

      const res = await fetch(`${FLASK_API_URL}/api/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, process_selector_id: inputQuizId })
      });
      
      const data = await res.json();

      if (data.overall_summary) {
        setFinalResults(data);
        stopCamera();
        socketRef.current?.disconnect();
        setQuizStatus('Quiz completed! View engagement dashboard for detailed analytics.');
        if (questionCount >= 5) {
          setShowDashboard(true);
        }
      } else if (data.question) {
        setQuestionData(data);
        setQuizStatus('Ready to start capture for this question.');
        setQuestionCount(prev => prev + 1);
      }
    } catch (error) {
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
    }
  };

  const startCaptureCycle = async () => {
    setLiveFeedLogs([]);
    
    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setCurrentQuestionStartTime(Date.now());
    
    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer(prev => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    await new Promise(resolve => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));
    
    setIsReadingQuestion(false);
    setQuestionTimer(null);
    
    const options = ['a', 'b', 'c', 'd'];
    
    for (let i = 0; i < options.length; i++) {
      setQuizStatus(`Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`);
      await processSingleOption(options[i]);
    }
    
    setQuizStatus('Capture complete for this question. Results sent.');
    
    setCurrentOptionTimer(null);
    setCurrentOption('');
  };

  const processSingleOption = (optionChar) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let frameCount = 0;
      
      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);
      
      timerIntervalRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);
        setCurrentOptionTimer(remaining);
      }, 1000);
      
      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);

      setTimeout(() => {
        clearInterval(intervalId);
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
        }
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };

  const captureAndSendFrame = (optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (!video || !canvas) {
      return;
    }
    
    if (!socketRef.current?.connected) {
      return;
    }

    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);

    socketRef.current.emit('process_frame', {
      quiz_id: quizId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    });
  };

  const setupSocketIO = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    
    socketRef.current = io(SOCKETIO_URL, {
      path: '/socketio2/',
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5
    });

    socketRef.current.on('connect', () => {
      setSocketStatus('Connected');
    });

    socketRef.current.on('disconnect', (reason) => {
      setSocketStatus('Disconnected');
    });

    socketRef.current.on('connect_error', (error) => {
      setSocketStatus('Connection Error');
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      setSocketStatus('Reconnected');
    });

    socketRef.current.on('reconnect_error', (error) => {
    });

    socketRef.current.on('reconnect_failed', () => {
      setSocketStatus('Reconnection Failed');
    });

    socketRef.current.on('hand_raised', (data) => {
      setLiveFeedLogs((logs) => {
        const newLog = {
          ...data,
          responseTime: currentQuestionStartTime ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000) : 0
        };
        return [...logs, newLog];
      });
    });

    socketRef.current.onAny((eventName, ...args) => {
    });
  };

  const initCamera = async () => {
    if (videoStreamRef.current) return;
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          width: { ideal: 1280 }, 
          height: { ideal: 720 } 
        } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
      }
    } catch (error) {
      setError(`Camera error: ${error.message}`);
    }
  };

  const stopCamera = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const testSocketConnection = () => {
    if (socketRef.current) {
      socketRef.current.emit('test_connection', { message: 'Hello from client' });
    }
  };

  const handleCloseDashboard = () => {
    setShowDashboard(false);
  };

  const handleManualQuizStart = () => {
    if (manualQuizId.trim()) {
      setQuizId(manualQuizId.trim());
      setShowManualInput(false);
      // Start quiz with manual ID
      startQuizWithId(manualQuizId.trim());
    } else {
      setError('Please enter a valid Quiz ID');
    }
  };

  const startQuizWithId = async (quizIdToUse) => {
    try {
      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizIdToUse }),
      });

      const data = await response.json();
      if (!response.ok || data.error) throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  useEffect(() => {
    if (inputQuizId) {
      startQuiz();
    }
  }, [inputQuizId]);

  if (showDashboard && quizId) {
    return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
  }

  return (
    <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-white/10 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Live Quiz</h2>
        <button onClick={onClose} className="text-white hover:text-blue-400">
          <X size={24} />
        </button>
      </div>

      <div className={`p-4 rounded-lg mb-6 ${
        socketStatus === 'Connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        <strong>Socket Status:</strong> {socketStatus}
        {socketRef.current && (
          <button 
            onClick={testSocketConnection}
            className="ml-4 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Test Connection
          </button>
        )}
      </div>

      {error && (
        <div className="text-center text-red-400 p-4">
          <p>{error}</p>
        </div>
      )}

      {/* Manual Quiz ID Input */}
      {showManualInput && (
        <div className="bg-gray-800/50 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">Enter Quiz ID</h3>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Quiz ID
              </label>
              <input
                type="text"
                value={manualQuizId}
                onChange={(e) => setManualQuizId(e.target.value)}
                placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
                className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                onKeyDown={(e) => e.key === 'Enter' && handleManualQuizStart()}
              />
            </div>
            <button
              onClick={handleManualQuizStart}
              disabled={!manualQuizId.trim()}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 text-white rounded-lg font-semibold shadow-md transition-all duration-200 disabled:cursor-not-allowed"
            >
              Start Quiz
            </button>
          </div>
          <p className="text-gray-400 text-sm mt-2">
            Enter the Quiz ID provided by your teacher to start the quiz.
          </p>
        </div>
      )}

      {/* Show option to switch to manual input if quiz started automatically */}
      {!showManualInput && questionData && (
        <div className="mb-4">
          <button
            onClick={() => {
              setShowManualInput(true);
              setQuestionData(null);
              setQuizId(null);
              setError(null);
            }}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm"
          >
            Enter Different Quiz ID
          </button>
        </div>
      )}

      {questionData && (
        <div className="text-white">
          <div className="status-bar p-4 bg-gray-800/50 rounded-lg mb-4">
            <div><strong>Quiz ID:</strong> {quizId}</div>
            <div><strong>Status:</strong> {quizStatus}</div>
          </div>

          <div className="flex gap-4 mb-4">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full max-w-xl rounded-lg border border-gray-700"
            />
            <canvas ref={canvasRef} className="hidden" />

            {liveFeedLogs.length > 0 && (
              <div className="w-80 bg-gray-800/90 rounded-lg p-4 border border-gray-600 max-h-96 overflow-y-auto">
                <h3 className="text-lg font-semibold mb-3 text-white">Live Hand Raises ({liveFeedLogs.length})</h3>
                <div className="space-y-2">
                  {liveFeedLogs
                    .sort((a, b) => new Date(a.detection_timestamp) - new Date(b.detection_timestamp))
                    .map((log, idx) => (
                      <div key={idx} className="bg-gray-700/50 p-2 rounded-lg text-sm">
                        <div className="flex justify-between items-start mb-1">
                          <span className="font-medium text-white">#{idx + 1} {log.student_name}</span>
                          <span className="text-xs text-gray-300">
                            {log.responseTime}s
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-blue-300">Option {log.option.toUpperCase()}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>

          <h2 className="text-xl font-semibold mb-2">{questionData.sub_topic_name}</h2>
          {isReadingQuestion && questionTimer !== null && (
            <div className="bg-orange-600 text-white px-4 py-2 rounded-lg text-center font-bold mb-4">
              Question Reading Time: {questionTimer}s
            </div>
          )}
          <p className="text-lg mb-4">
            {questionData.question_number}. {questionData.question}
          </p>

          <div className="grid gap-2 mb-4">
            {questionData.options.map((opt, idx) => {
              const optionLetter = String.fromCharCode(97 + idx);
              const isCurrentOption = currentOption.toLowerCase() === optionLetter;

              return (
                <div key={idx} className="relative">
                  <div className={`p-2 rounded-lg ${isCurrentOption ? 'bg-green-600/50' : 'bg-black/30'}`}>
                    {opt}
                  </div>
                  {isCurrentOption && currentOptionTimer !== null && (
                    <div className="absolute top-2 right-2 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-bold animate-pulse">
                      {currentOptionTimer}s
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <button
            onClick={handleNextQuestion}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-500"
            disabled={quizStatus.includes('Capturing') || quizStatus.includes('Processing') || isReadingQuestion}
          >
            Start Capture & Go to Next Question
          </button>
        </div>
      )}

      {finalResults && questionCount < 5 && (
        <div className="card p-6 bg-gray-800/50 rounded-lg mt-4">
          <h3 className="text-lg font-semibold mb-4 text-white">Quiz Finished!</h3>

          <h4 className="text-md font-semibold mb-2 text-white">Final Raw Data:</h4>
          <pre className="bg-gray-900 p-2 rounded-lg text-gray-200 overflow-auto max-h-64 text-sm">
            {JSON.stringify(finalResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default LiveQuiz;
