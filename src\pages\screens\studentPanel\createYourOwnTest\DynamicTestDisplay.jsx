// import React, { useState, useEffect } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import { useTestStore } from './testStore';
// import { useCreateYourOwnTestBioSubmitTestMutation } from './bioTest.slice';
// import { useCreateYourOwnTestChemistrySubmitTestMutation } from './chemistry.Slice';
// import { useCreateYourOwnTestMathSubmitTestMutation } from './maths.Slice';
// import { useCreateYourOwnTestPhysicsSubmitTestMutation } from './physics.Slice';
// import { useNavigate } from 'react-router';

// const DynamicTestDisplay = () => {
//   const testData = useTestStore((state) => state.testData);
//   const [currentIndex, setCurrentIndex] = useState(0);
//   const [userAnswers, setUserAnswers] = useState({});
//   const testDuration = useTestStore((state) => state.testDuration);
//   const [timeLeft, setTimeLeft] = useState(
//     testDuration
//       ? (() => {
//           if (typeof testDuration === 'string') {
//             if (testDuration.includes('hour')) {
//               const hours = parseFloat(testDuration);
//               return Math.round(hours * 60 * 60); // Convert hours to seconds
//             }
//             if (testDuration.includes('minute')) {
//               const minutes = parseFloat(testDuration);
//               return Math.round(minutes * 60); // Convert minutes to seconds
//             }
//           }
//           if (typeof testDuration === 'number') {
//             return testDuration; // Return duration in seconds if already a number
//           }
//           return 60 * 60; // Default to 1 hour (3600 seconds) if invalid
//         })()
//       : 60 * 60 // Default to 1 hour (3600 seconds) if testDuration is falsy
//   );
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [showFeedback, setShowFeedback] = useState(false);
//   const [feedbackData, setFeedbackData] = useState(null);
//   const navigate = useNavigate();

//   const [submitBioTest] = useCreateYourOwnTestBioSubmitTestMutation();
//   const [submitChemistryTest] = useCreateYourOwnTestChemistrySubmitTestMutation();
//   const [submitMathTest] = useCreateYourOwnTestMathSubmitTestMutation();
//   const [submitPhysicsTest] = useCreateYourOwnTestPhysicsSubmitTestMutation();

//   const q = testData && testData.questions[currentIndex];
//   const selectedOption = q ? userAnswers[q.id] || null : null;

//   useEffect(() => {
//     if (timeLeft <= 0) return;
//     const timer = setInterval(() => {
//       setTimeLeft((prev) => prev - 1);
//     }, 1000);
//     return () => clearInterval(timer);
//   }, [timeLeft]);

//   useEffect(() => {
//     if (timeLeft <= 0 && !isSubmitting && !showFeedback) {
//       handleSubmitTest();
//     }
//   }, [timeLeft]);

//   if (!testData) return <div style={{ color: 'red' }}>No test data found</div>;
//   if (!Array.isArray(testData.questions) || testData.questions.length === 0)
//     return <div style={{ color: 'red' }}>No questions found</div>;

//   const minutes = Math.floor(timeLeft / 60);
//   const seconds = timeLeft % 60;

//   const renderOptions = (options) => {
//     if (Array.isArray(options)) {
//       return options.map((opt, i) => (
//         <motion.li
//           key={i}
//           initial={{ opacity: 0, scale: 0.95 }}
//           animate={{ opacity: 1, scale: 1 }}
//           transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
//           className="mb-3">
//           <label
//             className={`flex items-center p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
//               selectedOption === opt
//                 ? 'border-indigo-500 bg-indigo-50 shadow-md'
//                 : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
//             }`}>
//             <input
//               type="radio"
//               name={`question-${q.id || currentIndex}`}
//               value={opt}
//               checked={selectedOption === opt}
//               onChange={() => handleOptionChange(opt)}
//               className="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
//             />
//             <span className="ml-3 text-gray-800 text-lg">{opt}</span>
//           </label>
//         </motion.li>
//       ));
//     } else if (options && typeof options === 'object') {
//       return Object.entries(options).map(([key, value], i) => (
//         <motion.li
//           key={key}
//           initial={{ opacity: 0, scale: 0.95 }}
//           animate={{ opacity: 1, scale: 1 }}
//           transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
//           className="mb-3">
//           <label
//             className={`flex items-start p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
//               selectedOption === key
//                 ? 'border-indigo-500 bg-indigo-50 shadow-md'
//                 : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
//             }`}>
//             <input
//               type="radio"
//               name={`question-${q.id || currentIndex}`}
//               value={key}
//               checked={selectedOption === key}
//               onChange={() => handleOptionChange(key)}
//               className="mt-1 h-5 w-5 text-indigo-600 focus:ring-indigo-500"
//             />
//             <div className="ml-3">
//               <span className="font-semibold text-gray-800">{key}.</span>
//               <span className="ml-2 text-gray-800 text-lg">{value}</span>
//             </div>
//           </label>
//         </motion.li>
//       ));
//     } else {
//       return (
//         <motion.li
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           className="text-red-500 p-4 text-lg">
//           No options available
//         </motion.li>
//       );
//     }
//   };

//   const handleOptionChange = (option) => {
//     setUserAnswers((prev) => ({ ...prev, [q.id]: option }));
//   };

//   const handleNext = () => {
//     setCurrentIndex((i) => i + 1);
//   };

//   const handlePrevious = () => {
//     setCurrentIndex((i) => i - 1);
//   };

//   const handleSubmitTest = async () => {
//     setIsSubmitting(true);
//     try {
//       const formattedAnswers = Object.entries(userAnswers).map(([questionId, selectedOption]) => ({
//         question_id: questionId,
//         selected_option: selectedOption
//       }));

//       const submissionPayload = {
//         exam_id: testData.exam_id,
//         user_id: testData.user_id,
//         answers: formattedAnswers
//       };

//       const subject = (testData.subject || '').trim().toLowerCase();

//       if (!subject) {
//         console.error(
//           'Subject is undefined or empty. testData:',
//           JSON.stringify(testData, null, 2)
//         );
//         alert('Error: Test subject is not defined.');
//         return;
//       }

//       let response;
//       if (['physics'].includes(subject)) {
//         response = await submitPhysicsTest(submissionPayload).unwrap();
//       } else if (['chemistry'].includes(subject)) {
//         response = await submitChemistryTest(submissionPayload).unwrap();
//       } else if (['mathematics', 'math', 'maths'].includes(subject)) {
//         response = await submitMathTest(submissionPayload).unwrap();
//       } else if (['biology', 'bio'].includes(subject)) {
//         response = await submitBioTest(submissionPayload).unwrap();
//       } else {
//         alert('Error: Unknown subject: ' + subject);
//         return;
//       }

//       console.log('Test submission response:', JSON.stringify(response, null, 2));

//       if (response && response.score !== undefined) {
//         alert(
//           `Test submitted successfully! Your score: ${response.score}/${
//             response.total_questions || testData.num_questions
//           }`
//         );
//       } else {
//         alert('Test submitted successfully!');
//       }
//       if (response && response.ai_feedback?.detailed_question_feedback) {
//         setFeedbackData(response);
//         setShowFeedback(true);
//       } else {
//         console.error('Invalid feedback data:', response);
//         alert('Error: Invalid feedback data received.');
//       }
//     } catch (error) {
//       console.error('Test submission failed:', error);
//       alert('Failed to submit test. Please try again.');
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const handleclose = () => {
//     setShowFeedback(false);
//     navigate('/sasthra');
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-10 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-4 gap-8">
//         <div className="lg:col-span-3 space-y-8">
//           <motion.div
//             initial={{ opacity: 0, y: -20 }}
//             animate={{ opacity: 1, y: 0 }}
//             transition={{ type: 'spring', stiffness: 120 }}
//             className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-2xl shadow-lg p-6">
//             <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
//               <div>
//                 <h1 className="text-2xl font-bold">{testData.exam_name}</h1>
//                 <div className="flex items-center mt-2 space-x-4 text-indigo-100">
//                   <p>
//                     Question <span className="font-bold">{currentIndex + 1}</span> of{' '}
//                     {testData.num_questions}
//                   </p>
//                   <span className="text-indigo-200">|</span>
//                 </div>
//               </div>
//               <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
//                 <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
//                   />
//                 </svg>
//                 <span className="font-mono">
//                   {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
//                 </span>
//               </div>
//             </div>
//             <div className="mt-4">
//               <div className="flex justify-between text-sm text-indigo-100 mb-1">
//                 <span>Progress</span>
//                 <span>{Math.round(((currentIndex + 1) / testData.num_questions) * 100)}%</span>
//               </div>
//               <div className="h-2 bg-indigo-700/30 rounded-full overflow-hidden">
//                 <motion.div
//                   className="h-full bg-white"
//                   initial={{ width: 0 }}
//                   animate={{ width: `${((currentIndex + 1) / testData.num_questions) * 100}%` }}
//                   transition={{ duration: 0.5, ease: 'easeOut' }}
//                 />
//               </div>
//             </div>
//           </motion.div>

//           <motion.div
//             key={currentIndex}
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             exit={{ opacity: 0, y: -20 }}
//             transition={{ duration: 0.4, type: 'spring', stiffness: 100 }}
//             className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-shadow duration-300">
//             <div className="flex items-start gap-4">
//               <div className="flex-shrink-0 bg-indigo-100 text-indigo-800 font-bold rounded-full h-12 w-12 flex items-center justify-center text-xl">
//                 {currentIndex + 1}
//               </div>
//               <div className="flex-1">
//                 <h3 className="text-xl font-semibold text-gray-900">{q.question_text}</h3>
//               </div>
//             </div>

//             {q.image_url && (
//               <motion.div
//                 initial={{ opacity: 0, scale: 0.95 }}
//                 animate={{ opacity: 1, scale: 1 }}
//                 transition={{ delay: 0.2 }}
//                 className="mt-6">
//                 <img
//                   src={q.image_url}
//                   alt="Question diagram"
//                   className="max-w-full h-auto max-h-64 rounded-lg shadow-sm mx-auto"
//                 />
//               </motion.div>
//             )}

//             <ul className="mt-6 space-y-2">{renderOptions(q.options)}</ul>
//           </motion.div>

//           <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
//             <motion.button
//               whileHover={{ scale: 1.05 }}
//               whileTap={{ scale: 0.95 }}
//               onClick={handlePrevious}
//               disabled={currentIndex === 0}
//               className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
//                 currentIndex === 0
//                   ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
//                   : 'bg-indigo-100 text-indigo-600 hover:cursor-pointer hover:bg-indigo-200'
//               }`}>
//               <div className="flex items-center gap-2">
//                 <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M15 19l-7-7 7-7"
//                   />
//                 </svg>
//                 Previous
//               </div>
//             </motion.button>

//             {currentIndex === testData.questions.length - 1 ? (
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={handleSubmitTest}
//                 disabled={isSubmitting}
//                 className="px-6 py-3 bg-green-600 hover:cursor-pointer text-white rounded-xl font-semibold hover:bg-green-700 transition-colors flex items-center gap-2">
//                 <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                   <path
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="M5 13l4 4L19 7"
//                   />
//                 </svg>
//                 Submit Test
//               </motion.button>
//             ) : (
//               <motion.button
//                 whileHover={{ scale: 1.05 }}
//                 whileTap={{ scale: 0.95 }}
//                 onClick={handleNext}
//                 disabled={currentIndex === testData.questions.length - 1}
//                 className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
//                   currentIndex === testData.questions.length - 1
//                     ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
//                     : 'bg-indigo-600 text-white hover:cursor-pointer hover:bg-indigo-700'
//                 }`}>
//                 <div className="flex items-center gap-2">
//                   Next
//                   <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                     <path
//                       strokeLinecap="round"
//                       strokeLinejoin="round"
//                       strokeWidth={2}
//                       d="M9 5l7 7-7 7"
//                     />
//                   </svg>
//                 </div>
//               </motion.button>
//             )}
//           </div>
//         </div>

//         <div className="lg:col-span-1 bg-white rounded-2xl shadow-xl p-6 h-full overflow-y-auto">
//           <h3 className="text-xl font-semibold text-gray-900 mb-4">Question Navigation</h3>
//           <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
//             {testData.questions.map((_, index) => {
//               const isAnswered = !!userAnswers[testData.questions[index].id];
//               const isCurrent = index === currentIndex;
//               return (
//                 <motion.button
//                   key={index}
//                   whileHover={{ scale: 1.1 }}
//                   whileTap={{ scale: 0.9 }}
//                   onClick={() => setCurrentIndex(index)}
//                   className={`h-12 w-12 hover:cursor-pointer rounded-full flex items-center justify-center text-lg font-semibold transition-colors ${
//                     isCurrent
//                       ? 'bg-indigo-600 text-white'
//                       : isAnswered
//                         ? 'bg-green-100 text-green-700'
//                         : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
//                   }`}
//                   aria-label={`Go to question ${index + 1}, ${isAnswered ? 'answered' : 'unanswered'}`}>
//                   {index + 1}
//                 </motion.button>
//               );
//             })}
//           </div>
//         </div>

//         <AnimatePresence>
//           {isSubmitting && (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               exit={{ opacity: 0 }}
//               transition={{ duration: 0.4 }}
//               className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden"
//               style={{
//                 background: 'radial-gradient(ellipse at 60% 40%, #2563eb 0%, #0a192f 100%)'
//               }}>
//               {[...Array(18)].map((_, i) => (
//                 <motion.div
//                   key={i}
//                   className="absolute rounded-full"
//                   style={{
//                     width: `${Math.random() * 32 + 16}px`,
//                     height: `${Math.random() * 32 + 16}px`,
//                     left: `${Math.random() * 100}%`,
//                     top: `${Math.random() * 100}%`,
//                     background: 'rgba(255,255,255,0.08)',
//                     filter: 'blur(2px)'
//                   }}
//                   animate={{
//                     y: [0, Math.random() * 40 - 20, 0],
//                     x: [0, Math.random() * 40 - 20, 0],
//                     opacity: [0.7, 1, 0.7]
//                   }}
//                   transition={{
//                     duration: 3 + Math.random() * 2,
//                     repeat: Infinity,
//                     delay: i * 0.2
//                   }}
//                 />
//               ))}
//               <motion.div
//                 className="relative z-10 bg-white/10 backdrop-blur-2xl border border-blue-200/30 rounded-3xl shadow-2xl flex flex-col items-center px-12 py-10"
//                 initial={{ scale: 0.95, y: 30 }}
//                 animate={{ scale: 1, y: 0 }}
//                 transition={{ type: 'spring', stiffness: 200 }}
//                 style={{
//                   boxShadow: '0 8px 40px 0 #2563eb44, 0 1.5px 8px 0 #0002'
//                 }}>
//                 <motion.div
//                   className="mb-8"
//                   animate={{ rotate: 360 }}
//                   transition={{ repeat: Infinity, duration: 8, ease: 'linear' }}>
//                   <svg width="100" height="100" viewBox="0 0 100 100">
//                     <circle cx="50" cy="50" r="18" fill="#2563eb" fillOpacity="0.15" />
//                     <ellipse
//                       cx="50"
//                       cy="50"
//                       rx="32"
//                       ry="18"
//                       fill="none"
//                       stroke="#2563eb"
//                       strokeWidth="2"
//                       opacity="0.5"
//                     />
//                     <ellipse
//                       cx="50"
//                       cy="50"
//                       rx="18"
//                       ry="32"
//                       fill="none"
//                       stroke="#fff"
//                       strokeWidth="1.5"
//                       opacity="0.3"
//                     />
//                     <motion.circle
//                       cx="82"
//                       cy="50"
//                       r="4"
//                       fill="#fff"
//                       animate={{ cy: [50, 32, 50, 68, 50] }}
//                       transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
//                     />
//                     <motion.circle
//                       cx="50"
//                       cy="18"
//                       r="3"
//                       fill="#2563eb"
//                       animate={{ cx: [50, 68, 50, 32, 50] }}
//                       transition={{
//                         repeat: Infinity,
//                         duration: 2.5,
//                         ease: 'easeInOut',
//                         delay: 0.5
//                       }}
//                     />
//                     <motion.circle
//                       cx="18"
//                       cy="50"
//                       r="2.5"
//                       fill="#fff"
//                       animate={{ cy: [50, 68, 50, 32, 50] }}
//                       transition={{ repeat: Infinity, duration: 2.2, ease: 'easeInOut', delay: 1 }}
//                     />
//                   </svg>
//                 </motion.div>
//                 <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
//                   Feedback Generating
//                 </h2>
//                 <p className="text-blue-100 mb-6 text-center max-w-xs">
//                   Evaluating your answers with generating Feedback. Please wait...
//                 </p>
//                 <div className="w-64 h-4 bg-blue-900/40 rounded-full overflow-hidden mb-8 relative">
//                   <motion.div
//                     className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-400 via-blue-300 to-white"
//                     initial={{ width: 0 }}
//                     animate={{ width: '100%' }}
//                     transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
//                   />
//                   <svg className="absolute left-0 top-0 w-full h-full" viewBox="0 0 256 16">
//                     <polyline
//                       points="0,8 16,4 32,12 48,6 64,10 80,4 96,12 112,6 128,10 144,4 160,12 176,6 192,10 208,4 224,12 240,6 256,8"
//                       fill="none"
//                       stroke="#fff"
//                       strokeWidth="2"
//                       opacity="0.5"
//                     />
//                   </svg>
//                 </div>
//                 <div className="flex gap-6 mt-2">
//                   {[
//                     { name: 'Accuracy', icon: '✓', color: 'text-green-400' },
//                     { name: 'Speed', icon: '⚡', color: 'text-yellow-300' },
//                     { name: 'Depth', icon: '🔍', color: 'text-blue-200' }
//                   ].map((metric, i) => (
//                     <div
//                       key={metric.name}
//                       className="backdrop-blur-md bg-white/10 border border-blue-200/20 rounded-xl px-5 py-3 flex flex-col items-center shadow">
//                       <span className={`text-2xl mb-1 ${metric.color}`}>{metric.icon}</span>
//                       <span className="text-xs text-white/80">{metric.name}</span>
//                     </div>
//                   ))}
//                 </div>
//               </motion.div>
//             </motion.div>
//           )}
//         </AnimatePresence>

//         <AnimatePresence>
//           {showFeedback && feedbackData && (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               exit={{ opacity: 0 }}
//               transition={{ duration: 0.3 }}
//               className="fixed inset-0 z-50 flex items-center justify-center p-4"
//               onClick={() => setShowFeedback(false)}>
//               {/* Floating abstract shapes background */}
//               <div className="absolute inset-0 overflow-hidden">
//                 {[...Array(8)].map((_, i) => (
//                   <motion.div
//                     key={i}
//                     className="absolute rounded-full bg-blue-100/30"
//                     style={{
//                       width: `${Math.random() * 200 + 100}px`,
//                       height: `${Math.random() * 200 + 100}px`,
//                       left: `${Math.random() * 100}%`,
//                       top: `${Math.random() * 100}%`,
//                       filter: 'blur(40px)'
//                     }}
//                     animate={{
//                       x: [0, Math.random() * 100 - 50],
//                       y: [0, Math.random() * 100 - 50],
//                       opacity: [0.3, 0.6, 0.3]
//                     }}
//                     transition={{
//                       duration: 10 + Math.random() * 10,
//                       repeat: Infinity,
//                       repeatType: 'reverse'
//                     }}
//                   />
//                 ))}
//               </div>

//               <motion.div
//                 className="relative bg-white rounded-3xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col border border-gray-100"
//                 onClick={(e) => e.stopPropagation()}
//                 initial={{ scale: 0.95, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 exit={{ scale: 0.95, opacity: 0 }}
//                 transition={{ type: 'spring', damping: 20 }}>
//                 {/* Minimal header with progress indicator */}
//                 <div className="relative h-2 bg-gray-100">
//                   <motion.div
//                     className="absolute top-0 left-0 h-full bg-blue-500"
//                     initial={{ width: 0 }}
//                     animate={{
//                       width: `${(feedbackData.score_summary.score / feedbackData.score_summary.total_questions) * 100}%`
//                     }}
//                     transition={{ duration: 1.5, ease: 'easeOut' }}
//                   />
//                 </div>

//                 {/* Main content area */}
//                 <div className="p-8 flex-1 overflow-y-auto">
//                   {/* Score visualization */}
//                   <div className="flex flex-col items-center mb-8">
//                     <div className="relative mb-6">
//                       <div className="w-40 h-40 rounded-full bg-blue-50 flex items-center justify-center">
//                         <motion.div
//                           className="absolute inset-0 rounded-full border-[12px] border-blue-100"
//                           animate={{
//                             scale: [1, 1.05, 1],
//                             opacity: [0.7, 1, 0.7]
//                           }}
//                           transition={{
//                             duration: 3,
//                             repeat: Infinity,
//                             ease: 'easeInOut'
//                           }}
//                         />
//                         <div className="relative z-10 text-center">
//                           <motion.div
//                             className="text-5xl font-bold text-blue-600 mb-1"
//                             initial={{ scale: 0.5, opacity: 0 }}
//                             animate={{ scale: 1, opacity: 1 }}
//                             transition={{ delay: 0.3 }}>
//                             {feedbackData.score_summary.score}
//                           </motion.div>
//                           <div className="text-gray-500">
//                             out of {feedbackData.score_summary.total_questions}
//                           </div>
//                         </div>
//                       </div>
//                     </div>

//                     {/* Performance chips */}
//                     <div className="flex gap-3 mb-8">
//                       {[
//                         {
//                           value: feedbackData.score_summary.num_correct,
//                           label: 'Correct',
//                           bg: 'bg-emerald-500/10',
//                           text: 'text-emerald-600',
//                           icon: '✓'
//                         },
//                         {
//                           value: feedbackData.score_summary.num_incorrect,
//                           label: 'Wrong',
//                           bg: 'bg-rose-500/10',
//                           text: 'text-rose-600',
//                           icon: '✗'
//                         },
//                         {
//                           value: feedbackData.score_summary.num_unattempted,
//                           label: 'Skipped',
//                           bg: 'bg-amber-500/10',
//                           text: 'text-amber-600',
//                           icon: '—'
//                         }
//                       ].map((item, i) => (
//                         <motion.div
//                           key={item.label}
//                           className={`px-4 py-2 rounded-full ${item.bg} ${item.text} flex items-center gap-2`}
//                           initial={{ opacity: 0, y: 20 }}
//                           animate={{ opacity: 1, y: 0 }}
//                           transition={{ delay: 0.4 + i * 0.1 }}>
//                           <span className="font-medium">{item.icon}</span>
//                           <span className="font-bold">{item.value}</span>
//                           <span className="text-sm">{item.label}</span>
//                         </motion.div>
//                       ))}
//                     </div>
//                   </div>

//                   {/* Question Feedback Content */}

//                   <motion.div
//                     initial={{ opacity: 0, y: 10 }}
//                     animate={{ opacity: 1, y: 0 }}
//                     transition={{ duration: 0.3 }}
//                     className="space-y-4">
//                     <h3 className="text-lg font-semibold text-gray-800 mb-4">Question Feedback</h3>
//                     {feedbackData?.ai_feedback?.detailed_question_feedback?.length > 0 ? (
//                       feedbackData.ai_feedback.detailed_question_feedback.map((question, index) => (
//                         <motion.div
//                           key={question.question_id}
//                           className="p-4 bg-gray-50 rounded-lg border border-gray-100"
//                           initial={{ opacity: 0, x: -10 }}
//                           animate={{ opacity: 1, x: 0 }}
//                           transition={{ delay: index * 0.1 }}>
//                           <h4 className="text-md font-medium text-gray-700">
//                             Question {question.question_id}
//                           </h4>
//                           <p className="text-sm text-gray-600 mt-1">
//                             {question.feedback_on_answer}
//                           </p>
//                           <p className="text-sm text-gray-600 mt-1">
//                             <span className="font-medium">Answer: </span>
//                             {question.validation ? (
//                               <span className="text-emerald-600">
//                                 Correct Answer: {question.correct_answer}
//                               </span>
//                             ) : (
//                               <span className="text-rose-600">
//                                 Correct Answer: {question.correct_answer}, Your Answer:{' '}
//                                 {question.user_answer}
//                               </span>
//                             )}
//                           </p>
//                           <p className="text-sm text-gray-500 mt-1">
//                             <span className="font-medium">Tip: </span>
//                             {question.reinforce_concept_from_solution}
//                           </p>
//                         </motion.div>
//                       ))
//                     ) : (
//                       <p className="text-sm text-gray-600">No feedback available.</p>
//                     )}
//                   </motion.div>
//                 </div>

//                 {/* Floating action button */}
//                 <div className="p-6 pt-0">
//                   <motion.button
//                     onClick={handleclose}
//                     className="w-full py-4 px-6 bg-blue-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all flex items-center justify-center gap-3"
//                     whileHover={{ y: -2 }}
//                     whileTap={{ scale: 0.98 }}>
//                     <span>Continue Practice</span>
//                     <motion.svg
//                       width="20"
//                       height="20"
//                       viewBox="0 0 24 24"
//                       fill="none"
//                       stroke="currentColor"
//                       animate={{ x: [0, 4, 0] }}
//                       transition={{ duration: 1.5, repeat: Infinity }}>
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         strokeWidth="2"
//                         d="M14 5l7 7m0 0l-7 7m7-7H3"
//                       />
//                     </motion.svg>
//                   </motion.button>
//                 </div>
//               </motion.div>
//             </motion.div>
//           )}
//         </AnimatePresence>
//       </div>
//     </div>
//   );
// };

// const Tabs = ({ feedbackData }) => {
//   const [tab, setTab] = React.useState('insights');
//   const [openQuestions, setOpenQuestions] = React.useState([]);

//   const toggleQuestion = (index) => {
//     setOpenQuestions((prev) =>
//       prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
//     );
//   };

//   return (
//     <div>
//       <div className="flex justify-center gap-3 mb-6">
//         {['insights', 'analysis', 'tips'].map((t) => (
//           <motion.button
//             key={t}
//             className={`px-4 py-2 rounded-lg font-semibold text-white transition-all ${
//               tab === t ? 'bg-blue-600 shadow-lg' : 'bg-blue-800/50 hover:bg-blue-700/70'
//             }`}
//             onClick={() => setTab(t)}
//             whileHover={{ scale: 1.05 }}
//             whileTap={{ scale: 0.95 }}>
//             {t.charAt(0).toUpperCase() + t.slice(1)}
//           </motion.button>
//         ))}
//       </div>
//       <AnimatePresence mode="wait">
//         {tab === 'insights' && (
//           <motion.div
//             key="insights"
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             exit={{ opacity: 0, y: -20 }}
//             transition={{ duration: 0.3 }}
//             className="bg-white/20 rounded-xl p-6 text-black">
//             <h3 className="text-xl font-bold mb-3">Performance Insights</h3>
//             <p className="text-gray">{feedbackData.ai_feedback.overall_assessment}</p>
//           </motion.div>
//         )}
//         {tab === 'analysis' && (
//           <motion.div
//             key="analysis"
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             exit={{ opacity: 0, y: -20 }}
//             transition={{ duration: 0.3 }}
//             className="space-y-4">
//             <h3 className="text-xl font-bold text-black">Question Analysis</h3>
//             {feedbackData.ai_feedback.detailed_question_feedback.map((item, idx) => (
//               <motion.div
//                 key={idx}
//                 className="bg-white/10 rounded-xl overflow-hidden border border-blue-400/30"
//                 initial={{ opacity: 0, y: 10 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ delay: idx * 0.1 }}>
//                 <button
//                   className="w-full text-left p-4 bg-blue-800/50 hover:bg-blue-700/50 flex justify-between items-center"
//                   onClick={() => toggleQuestion(idx)}>
//                   <span className="font-semibold  text-white">Question {item.question_id}</span>
//                   <motion.span
//                     animate={{ rotate: openQuestions.includes(idx) ? 180 : 0 }}
//                     transition={{ duration: 0.3 }}>
//                     ▼
//                   </motion.span>
//                 </button>
//                 <AnimatePresence>
//                   {openQuestions.includes(idx) && (
//                     <motion.div
//                       initial={{ height: 0, opacity: 0 }}
//                       animate={{ height: 'auto', opacity: 1 }}
//                       exit={{ height: 0, opacity: 0 }}
//                       transition={{ duration: 0.3 }}
//                       className="p-4 text-gray">
//                       <p className="mb-2">
//                         <span className="font-medium text-[var(--color-student)]">Feedback: </span>
//                         {item.feedback_on_answer}
//                       </p>
//                       <p>
//                         <span className="font-medium text-[var(--color-student)]">
//                           Key Concept:{' '}
//                         </span>
//                         {item.reinforce_concept_from_solution}
//                       </p>
//                     </motion.div>
//                   )}
//                 </AnimatePresence>
//               </motion.div>
//             ))}
//           </motion.div>
//         )}
//         {tab === 'tips' && (
//           <motion.div
//             key="tips"
//             initial={{ opacity: 0, y: 20 }}
//             animate={{ opacity: 1, y: 0 }}
//             exit={{ opacity: 0, y: -20 }}
//             transition={{ duration: 0.3 }}
//             className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="bg-white/10 rounded-xl p-4 border border-red-400/30">
//               <h4 className="text-lg font-bold text-[var(--color-student)] mb-2">Focus Areas</h4>
//               <ul className="list-disc pl-5 text-gray-500">
//                 {feedbackData.ai_feedback.areas_for_improvement.map((item, idx) => (
//                   <li key={idx} className="mb-1">
//                     {item}
//                   </li>
//                 ))}
//               </ul>
//             </div>
//             <div className="bg-white/10 rounded-xl p-4 border border-green-400/30">
//               <h4 className="text-lg font-bold text-green-700 mb-2">Study Strategies</h4>
//               <ul className="list-disc pl-5 text-gray-500">
//                 {feedbackData.ai_feedback.general_study_tips.map((item, idx) => (
//                   <li key={idx} className="mb-1">
//                     {item}
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           </motion.div>
//         )}
//       </AnimatePresence>
//     </div>
//   );
// };

// export default DynamicTestDisplay;
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTestStore } from './testStore';
import {
  FileText,
  X,
  Check,
  AlertTriangle,
  Award,
  ArrowRight,
  Clock,
  ChevronRight,
  Circle,
  Lightbulb,
  Star,
  TrendingUp,
  List
} from 'lucide-react';
import { useCreateYourOwnTestBioSubmitTestMutation } from './bioTest.slice';
import { useCreateYourOwnTestChemistrySubmitTestMutation } from './chemistry.Slice';
import { useCreateYourOwnTestMathSubmitTestMutation } from './maths.Slice';
import { useCreateYourOwnTestPhysicsSubmitTestMutation } from './physics.Slice';
import { useNavigate } from 'react-router';

const DynamicTestDisplay = () => {
  const testData = useTestStore((state) => state.testData);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState({});
  const testDuration = useTestStore((state) => state.testDuration);
  const [timeLeft, setTimeLeft] = useState(
    testDuration
      ? (() => {
          if (typeof testDuration === 'string') {
            if (testDuration.includes('hour')) {
              const hours = parseFloat(testDuration);
              return Math.round(hours * 60 * 60);
            }
            if (testDuration.includes('minute')) {
              const minutes = parseFloat(testDuration);
              return Math.round(minutes * 60);
            }
          }
          if (typeof testDuration === 'number') return testDuration;
          return 60 * 60; // Default to 1 hour
        })()
      : 60 * 60
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackData, setFeedbackData] = useState(null);
  const navigate = useNavigate();

  const [submitBioTest] = useCreateYourOwnTestBioSubmitTestMutation();
  const [submitChemistryTest] = useCreateYourOwnTestChemistrySubmitTestMutation();
  const [submitMathTest] = useCreateYourOwnTestMathSubmitTestMutation();
  const [submitPhysicsTest] = useCreateYourOwnTestPhysicsSubmitTestMutation();

  const q = testData && testData.questions[currentIndex];
  const selectedOption = q ? userAnswers[q.id] || null : null;

  useEffect(() => {
    if (timeLeft <= 0) return;
    const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
    return () => clearInterval(timer);
  }, [timeLeft]);

  useEffect(() => {
    if (timeLeft <= 0 && !isSubmitting && !showFeedback) handleSubmitTest();
  }, [timeLeft]);

  if (!testData) return <div style={{ color: 'red' }}>No test data found</div>;
  if (!Array.isArray(testData.questions) || testData.questions.length === 0)
    return <div style={{ color: 'red' }}>No questions found</div>;

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  const renderOptions = (options) => {
    if (Array.isArray(options)) {
      return options.map((opt, i) => (
        <motion.li
          key={i}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
          className="mb-3">
          <label
            className={`flex items-center p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
              selectedOption === opt
                ? 'border-indigo-500 bg-indigo-50 shadow-md'
                : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
            }`}>
            <input
              type="radio"
              name={`question-${q.id || currentIndex}`}
              value={opt}
              checked={selectedOption === opt}
              onChange={() => handleOptionChange(opt)}
              className="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <span className="ml-3 text-gray-800 text-lg">{opt}</span>
          </label>
        </motion.li>
      ));
    } else if (options && typeof options === 'object') {
      return Object.entries(options).map(([key, value], i) => (
        <motion.li
          key={key}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
          className="mb-3">
          <label
            className={`flex items-start p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
              selectedOption === key
                ? 'border-indigo-500 bg-indigo-50 shadow-md'
                : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
            }`}>
            <input
              type="radio"
              name={`question-${q.id || currentIndex}`}
              value={key}
              checked={selectedOption === key}
              onChange={() => handleOptionChange(key)}
              className="mt-1 h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <div className="ml-3">
              <span className="font-semibold text-gray-800">{key}.</span>
              <span className="ml-2 text-gray-800 text-lg">{value}</span>
            </div>
          </label>
        </motion.li>
      ));
    } else {
      return (
        <motion.li
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-red-500 p-4 text-lg">
          No options available
        </motion.li>
      );
    }
  };

  const handleOptionChange = (option) => {
    setUserAnswers((prev) => ({ ...prev, [q.id]: option }));
  };

  const handleNext = () => setCurrentIndex((i) => i + 1);
  const handlePrevious = () => setCurrentIndex((i) => i - 1);

  const handleSubmitTest = async () => {
    setIsSubmitting(true);
    try {
      const formattedAnswers = Object.entries(userAnswers).map(([questionId, selectedOption]) => ({
        question_id: questionId,
        selected_option: selectedOption
      }));

      const submissionPayload = {
        exam_id: testData.exam_id,
        user_id: testData.user_id,
        answers: formattedAnswers
      };

      const subject = (testData.subject || '').trim().toLowerCase();

      if (!subject) {
        console.error(
          'Subject is undefined or empty. testData:',
          JSON.stringify(testData, null, 2)
        );
        alert('Error: Test subject is not defined.');
        return;
      }

      let response;
      if (['physics'].includes(subject))
        response = await submitPhysicsTest(submissionPayload).unwrap();
      else if (['chemistry'].includes(subject))
        response = await submitChemistryTest(submissionPayload).unwrap();
      else if (['mathematics', 'math', 'maths'].includes(subject))
        response = await submitMathTest(submissionPayload).unwrap();
      else if (['biology', 'bio'].includes(subject))
        response = await submitBioTest(submissionPayload).unwrap();
      else {
        alert('Error: Unknown subject: ' + subject);
        return;
      }

      console.log('Test submission response:', JSON.stringify(response, null, 2));

      if (response && response.score !== undefined) {
        alert(
          `Test submitted successfully! Your score: ${response.score}/${response.total_questions || testData.num_questions}`
        );
      } else alert('Test submitted successfully!');

      if (
        response &&
        (response.detailed_question_feedback || response.ai_feedback?.detailed_question_feedback)
      ) {
        setFeedbackData(response);
        setShowFeedback(true);
      } else {
        console.error('Invalid feedback data:', response);
        alert('Error: Invalid feedback data received.');
      }
    } catch (error) {
      console.error('Test submission failed:', error);
      alert('Failed to submit test. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleclose = () => {
    setShowFeedback(false);
    navigate('/sasthra');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-10 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div className="lg:col-span-3 space-y-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: 'spring', stiffness: 120 }}
            className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-2xl shadow-lg p-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold">{testData.exam_name}</h1>
                <div className="flex items-center mt-2 space-x-4 text-indigo-100">
                  <p>
                    Question <span className="font-bold">{currentIndex + 1}</span> of{' '}
                    {testData.num_questions}
                  </p>
                  <span className="text-indigo-200">|</span>
                </div>
              </div>
              <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span className="font-mono">
                  {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
                </span>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-indigo-100 mb-1">
                <span>Progress</span>
                <span>{Math.round(((currentIndex + 1) / testData.num_questions) * 100)}%</span>
              </div>
              <div className="h-2 bg-indigo-700/30 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-white"
                  initial={{ width: 0 }}
                  animate={{ width: `${((currentIndex + 1) / testData.num_questions) * 100}%` }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}
                />
              </div>
            </div>
          </motion.div>

          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, type: 'spring', stiffness: 100 }}
            className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-shadow duration-300">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 bg-indigo-100 text-indigo-800 font-bold rounded-full h-12 w-12 flex items-center justify-center text-xl">
                {currentIndex + 1}
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{q.question_text}</h3>
              </div>
            </div>

            {q.image_url && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className="mt-6">
                <img
                  src={q.image_url}
                  alt="Question diagram"
                  className="max-w-full h-auto max-h-64 rounded-lg shadow-sm mx-auto"
                />
              </motion.div>
            )}

            <ul className="mt-6 space-y-2">{renderOptions(q.options)}</ul>
          </motion.div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
                currentIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-indigo-100 text-indigo-600 hover:bg-indigo-200 hover:cursor-pointer'
              }`}>
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Previous
              </div>
            </motion.button>

            {currentIndex === testData.questions.length - 1 ? (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleSubmitTest}
                disabled={isSubmitting}
                className="px-6 py-3 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Submit Test
              </motion.button>
            ) : (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleNext}
                disabled={currentIndex === testData.questions.length - 1}
                className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
                  currentIndex === testData.questions.length - 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-indigo-600 text-white hover:bg-indigo-700 hover:cursor-pointer'
                }`}>
                <div className="flex items-center gap-2">
                  Next
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </motion.button>
            )}
          </div>
        </div>

        <div className="lg:col-span-1 bg-white rounded-2xl shadow-xl p-6 h-full overflow-y-auto">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Question Navigation</h3>
          <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
            {testData.questions.map((_, index) => {
              const isAnswered = !!userAnswers[testData.questions[index].id];
              const isCurrent = index === currentIndex;
              return (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setCurrentIndex(index)}
                  className={`h-12 w-12 rounded-full flex items-center justify-center text-lg font-semibold transition-colors ${
                    isCurrent
                      ? 'bg-indigo-600 text-white'
                      : isAnswered
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  aria-label={`Go to question ${index + 1}, ${isAnswered ? 'answered' : 'unanswered'}`}>
                  {index + 1}
                </motion.button>
              );
            })}
          </div>
        </div>

        <AnimatePresence>
          {isSubmitting && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden"
              style={{
                background: 'radial-gradient(ellipse at 60% 40%, #2563eb 0%, #0a192f 100%)'
              }}>
              {[...Array(18)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute rounded-full"
                  style={{
                    width: `${Math.random() * 32 + 16}px`,
                    height: `${Math.random() * 32 + 16}px`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    background: 'rgba(255,255,255,0.08)',
                    filter: 'blur(2px)'
                  }}
                  animate={{
                    y: [0, Math.random() * 40 - 20, 0],
                    x: [0, Math.random() * 40 - 20, 0],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ duration: 3 + Math.random() * 2, repeat: Infinity, delay: i * 0.2 }}
                />
              ))}
              <motion.div
                className="relative z-10 bg-white/10 backdrop-blur-2xl border border-blue-200/30 rounded-3xl shadow-2xl flex flex-col items-center px-12 py-10"
                initial={{ scale: 0.95, y: 30 }}
                animate={{ scale: 1, y: 0 }}
                transition={{ type: 'spring', stiffness: 200 }}
                style={{ boxShadow: '0 8px 40px 0 #2563eb44, 0 1.5px 8px 0 #0002' }}>
                <motion.div
                  className="mb-8"
                  animate={{ rotate: 360 }}
                  transition={{ repeat: Infinity, duration: 8, ease: 'linear' }}>
                  <svg width="100" height="100" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="18" fill="#2563eb" fillOpacity="0.15" />
                    <ellipse
                      cx="50"
                      cy="50"
                      rx="32"
                      ry="18"
                      fill="none"
                      stroke="#2563eb"
                      strokeWidth="2"
                      opacity="0.5"
                    />
                    <ellipse
                      cx="50"
                      cy="50"
                      rx="18"
                      ry="32"
                      fill="none"
                      stroke="#fff"
                      strokeWidth="1.5"
                      opacity="0.3"
                    />
                    <motion.circle
                      cx="82"
                      cy="50"
                      r="4"
                      fill="#fff"
                      animate={{ cy: [50, 32, 50, 68, 50] }}
                      transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
                    />
                    <motion.circle
                      cx="50"
                      cy="18"
                      r="3"
                      fill="#2563eb"
                      animate={{ cx: [50, 68, 50, 32, 50] }}
                      transition={{
                        repeat: Infinity,
                        duration: 2.5,
                        ease: 'easeInOut',
                        delay: 0.5
                      }}
                    />
                    <motion.circle
                      cx="18"
                      cy="50"
                      r="2.5"
                      fill="#fff"
                      animate={{ cy: [50, 68, 50, 32, 50] }}
                      transition={{ repeat: Infinity, duration: 2.2, ease: 'easeInOut', delay: 1 }}
                    />
                  </svg>
                </motion.div>
                <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
                  Feedback Generating
                </h2>
                <p className="text-blue-100 mb-6 text-center max-w-xs">
                  Evaluating your answers with generating Feedback. Please wait...
                </p>
                <div className="w-64 h-4 bg-blue-900/40 rounded-full overflow-hidden mb-8 relative">
                  <motion.div
                    className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-400 via-blue-300 to-white"
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
                  />
                  <svg className="absolute left-0 top-0 w-full h-full" viewBox="0 0 256 16">
                    <polyline
                      points="0,8 16,4 32,12 48,6 64,10 80,4 96,12 112,6 128,10 144,4 160,12 176,6 192,10 208,4 224,12 240,6 256,8"
                      fill="none"
                      stroke="#fff"
                      strokeWidth="2"
                      opacity="0.5"
                    />
                  </svg>
                </div>
                <div className="flex gap-6 mt-2">
                  {[
                    { name: 'Accuracy', icon: '✓', color: 'text-green-400' },
                    { name: 'Speed', icon: '⚡', color: 'text-yellow-300' },
                    { name: 'Depth', icon: '🔍', color: 'text-blue-200' }
                  ].map((metric, i) => (
                    <div
                      key={metric.name}
                      className="backdrop-blur-md bg-white/10 border border-blue-200/20 rounded-xl px-5 py-3 flex flex-col items-center shadow">
                      <span className={`text-2xl mb-1 ${metric.color}`}>{metric.icon}</span>
                      <span className="text-xs text-white/80">{metric.name}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        <AnimatePresence>
          {showFeedback && feedbackData && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/30 backdrop-blur-sm"
              onClick={() => setShowFeedback(false)}>
              {/* Main Container */}
              <motion.div
                className="relative bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden border border-gray-200"
                initial={{ scale: 0.95, y: 20 }}
                animate={{ scale: 1, y: 0 }}
                exit={{ scale: 0.95, y: 20 }}
                onClick={(e) => e.stopPropagation()}>
                {/* Header */}
                <div className="p-6 border-b border-gray-200">
                  <div className="flex justify-between items-center mb-4">
                    <motion.h2
                      className="text-2xl font-bold flex items-center gap-2"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}>
                      <FileText className="text-indigo-600" />
                      Exam Results
                    </motion.h2>
                    <button
                      onClick={() => setShowFeedback(false)}
                      className="p-1 rounded-full hover:bg-gray-100">
                      <X className="text-gray-500" />
                    </button>
                  </div>

                  {/* Score Summary */}
                  <motion.div
                    className="flex items-center justify-between"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}>
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <motion.div
                          className="w-16 h-16 rounded-full bg-indigo-50 flex items-center justify-center"
                          animate={{ rotate: [0, 360] }}
                          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}>
                          <Circle className="text-indigo-200 absolute" size={64} />
                        </motion.div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-xl font-bold text-indigo-600">
                            {feedbackData.score_summary.score}/
                            {feedbackData.score_summary.total_questions}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Check className="text-green-500" size={16} />
                          <span>Correct: {feedbackData.score_summary.correct_answers}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <X className="text-red-500" size={16} />
                          <span>Incorrect: {feedbackData.score_summary.incorrect_answers}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="text-amber-500" size={16} />
                          <span>Time: {feedbackData.score_summary.time_taken}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Content */}
                <div className="overflow-y-auto max-h-[60vh] p-6">
                  {/* Questions */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}>
                    <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                      <List className="text-indigo-600" />
                      Question Feedback
                    </h3>
                    <div className="space-y-4">
                      {feedbackData.detailed_question_feedback?.map((feedback, i) => (
                        <motion.div
                          key={i}
                          className="p-4 rounded-lg border border-gray-200"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 * i }}>
                          <div className="flex gap-3">
                            <div
                              className={`p-2 rounded-full ${feedback.validation ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                              {feedback.validation ? <Check size={18} /> : <X size={18} />}
                            </div>
                            <div>
                              <h4 className="font-medium">Q{i + 1}</h4>
                              <p className="text-sm text-gray-600 mt-1">
                                {feedback.feedback_on_answer}
                              </p>

                              <div className="grid grid-cols-2 gap-2 mt-3">
                                <div className="bg-gray-50 p-2 rounded text-sm">
                                  <span className="font-medium">Your answer:</span>
                                  <span
                                    className={
                                      feedback.validation ? 'text-green-600' : 'text-red-600'
                                    }>
                                    {feedback.user_answer || 'Not attempted'}
                                  </span>
                                </div>
                                <div className="bg-gray-50 p-2 rounded text-sm">
                                  <span className="font-medium">Correct:</span>
                                  <span className="text-green-600">{feedback.correct_answer}</span>
                                </div>
                              </div>

                              {feedback.reinforce_concept_from_solution && (
                                <div className="mt-2 flex gap-2 bg-blue-50 p-2 rounded text-sm text-blue-600">
                                  <Lightbulb size={16} />
                                  <span>{feedback.reinforce_concept_from_solution}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Strengths & Improvements */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                    {/* Strengths */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}>
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <Star className="text-amber-500" />
                        Your Strengths
                      </h3>
                      <div className="space-y-2">
                        {feedbackData.topic_strengths?.map((strength, i) => (
                          <motion.div
                            key={i}
                            className="flex items-start gap-2 p-3 bg-green-50 rounded-lg"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 * i }}>
                            <Award className="text-green-500 mt-0.5 flex-shrink-0" size={16} />
                            <span className="text-sm">{strength}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>

                    {/* Improvements */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}>
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <TrendingUp className="text-indigo-500" />
                        Areas to Improve
                      </h3>
                      <div className="space-y-2">
                        {feedbackData.ai_feedback?.areas_for_improvement?.map((item, i) => (
                          <motion.div
                            key={i}
                            className="flex items-start gap-2 p-3 bg-amber-50 rounded-lg"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 * i }}>
                            <AlertTriangle
                              className="text-amber-500 mt-0.5 flex-shrink-0"
                              size={16}
                            />
                            <span className="text-sm">{item}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-200">
                  <motion.button
                    onClick={handleclose}
                    className="w-full py-3 px-4 bg-indigo-600 text-white rounded-lg font-medium flex items-center justify-center gap-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    Continue Learning
                    <motion.span
                      animate={{ x: [0, 4, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}>
                      <ArrowRight size={18} />
                    </motion.span>
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DynamicTestDisplay;
