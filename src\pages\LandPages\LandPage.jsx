import React, { useState, useRef, useEffect } from 'react';
import Logo from '../../assets/sasthra_logo.png';
import {
  motion,
  useScroll,
  useTransform,
  AnimatePresence,
  useMotionValue,
  animate
} from 'framer-motion';
import {
  Users,
  Target,
  BrainCircuit,
  GraduationCap,
  BookOpen,
  ChevronRight,
  Rocket,
  Bookmark,
  MapPin,
  Phone,
  Mail,
  BookOpenText,
  Twitter,
  Linkedin,
  Instagram,
  ChevronDown,
  HeartHandshake,
  Video,
  Youtube,
  Send,
  BarChart2,
  BarChart,
  X
} from 'lucide-react';

// Existing components (StatCard, FeatureCard, SplitPanel, FaqItem) remain unchanged
const StatCard = ({ icon, value, label, color, delay }) => (
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true, amount: 0.5 }}
    transition={{ duration: 0.6, delay: delay * 0.15 }}
    className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100 flex items-center space-x-4">
    <div
      className={`w-16 h-16 rounded-full flex items-center justify-center`}
      style={{ backgroundColor: color, color: '#fff' }}>
      {icon}
    </div>
    <div>
      <p className="text-3xl font-bold text-gray-900">{value}</p>
      <p className="text-gray-500">{label}</p>
    </div>
  </motion.div>
);

const FeatureCard = ({ icon, title, description, color }) => (
  <div className="p-8 rounded-3xl border border-gray-200 bg-white/50 backdrop-blur-lg">
    <div
      className="flex items-center justify-center w-14 h-14 rounded-2xl mb-5"
      style={{ backgroundColor: color }}>
      {icon}
    </div>
    <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 leading-relaxed">{description}</p>
  </div>
);

const SplitPanel = ({ title, description, bgImage, icon, isActive, onHover }) => {
  return (
    <motion.div
      onHoverStart={onHover}
      className="relative h-[70vh] flex-1 cursor-pointer overflow-hidden transition-all duration-700 ease-[cubic-bezier(0.22,1,0.36,1)] rounded-2xl"
      animate={{ flexGrow: isActive ? 2.5 : 1 }}>
      <motion.img
        src={bgImage}
        className="absolute inset-0 w-full h-full object-cover"
        alt={title}
        initial={{ scale: 1.1 }}
        animate={{ scale: isActive ? 1 : 1.1 }}
        transition={{ duration: 1, ease: [0.22, 1, 0.36, 1] }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />
      <div className="relative z-10 flex h-full flex-col justify-end p-8 text-white">
        <div className="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
          {icon}
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{
            opacity: isActive ? 1 : 0,
            y: isActive ? 0 : 20
          }}
          transition={{ duration: 0.4, ease: 'easeOut', delay: isActive ? 0.2 : 0 }}>
          <h3 className="text-2xl font-bold leading-tight">{title}</h3>
          <p className="mt-2 max-w-xs text-gray-200">{description}</p>
        </motion.div>
      </div>
    </motion.div>
  );
};

const FaqItem = ({ question, answer, index, activeFaqIndex, setActiveFaqIndex }) => {
  const themes = [
    { bg: '#f4c430', text: '#FFF', icon: '#0e6e66', name: 'parents' },
    { bg: '#000080', text: '#fff', icon: '#7d5a08', name: 'counselor' },
    { bg: '#f4c430', text: '#FFF', icon: '#7c4d06', name: 'trainee' },
    { bg: '#2563eb', text: '#FFF', icon: '#1e40af', name: 'student' },
    { bg: '#7d1e1c', text: '#4a1110', icon: '#4a1110', name: 'director' },
    { bg: '#000080', text: '#00004d', icon: '#00004d', name: 'teacher' }
  ];
  const theme = themes[index % themes.length];

  return (
    <motion.div
      className={`group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300`}
      style={{ backgroundColor: theme.bg }}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.6 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}>
      <motion.div
        className="absolute -right-10 -top-10 w-40 h-40 rounded-full opacity-10"
        style={{ backgroundColor: theme.text }}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.1, 0.2, 0.1]
        }}
        transition={{
          duration: 8 + index,
          repeat: Infinity
        }}
      />
      <div className="relative p-8">
        <div className="flex items-start">
          <motion.div
            className="flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center mr-6 mt-1 backdrop-blur-sm"
            style={{
              backgroundColor: `rgba(255,255,255,0.2)`,
              border: `2px solid ${theme.text}`
            }}
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            transition={{ delay: index * 0.1 + 0.2, type: 'spring' }}>
            <svg className="w-6 h-6" fill="none" stroke={theme.text} viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </motion.div>
          <div className="flex-1">
            <h3 className="text-xl font-bold mb-3" style={{ color: theme.text }}>
              {question}
            </h3>
            <motion.div
              className="overflow-hidden"
              initial={{ height: 0 }}
              animate={{
                height: activeFaqIndex === index ? 'auto' : 0
              }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}>
              <div className="pb-4" style={{ color: theme.text }}>
                <p>{answer}</p>
                {index % 2 === 0 && (
                  <div className="mt-4 flex items-center">
                    <div
                      className="w-10 h-10 rounded-full mr-3 flex items-center justify-center"
                      style={{
                        backgroundColor: `rgba(255,255,255,0.3)`,
                        border: `1px solid ${theme.text}`
                      }}>
                      <svg className="w-5 h-5" fill="none" stroke={theme.text} viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div className="text-sm font-medium" style={{ color: theme.text }}>
                        {
                          [
                            'Parent Advisor',
                            'Counselor',
                            'Trainer',
                            'Student Success',
                            'Director',
                            'Teacher'
                          ][index % 6]
                        }
                      </div>
                      <div className="text-xs" style={{ color: theme.text, opacity: 0.8 }}>
                        {theme.name} support
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
            <motion.button
              className="mt-2 flex items-center font-medium text-sm px-4 py-2 rounded-lg"
              style={{
                backgroundColor: `rgba(255,255,255,0.2)`,
                color: theme.text
              }}
              onClick={() => setActiveFaqIndex(activeFaqIndex === index ? null : index)}
              whileHover={{
                backgroundColor: `rgba(255,255,255,0.3)`
              }}
              whileTap={{ scale: 0.95 }}>
              {activeFaqIndex === index ? 'Hide answer' : 'Show answer'}
              <motion.svg
                className="ml-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                animate={{
                  rotate: activeFaqIndex === index ? 180 : 0
                }}>
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </motion.svg>
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const LandPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const faqData = [
    {
      question: 'How does the AI personalization work?',
      answer:
        'Our AI engine analyzes your performance on quizzes and mock tests in real-time. It identifies your conceptual weaknesses and strengths, then dynamically adjusts your study plan, recommending specific video lectures, reading materials, and practice problems to address your needs.'
    },
    {
      question: 'How is Sasthra different from other EdTech platforms?',
      answer:
        'Our core differentiator is the deep integration of our AI with expert human mentorship. We don’t just provide content; we provide a personalized and supportive ecosystem that adapts to you, ensuring your efforts are always focused on what matters most for your success.'
    },
    {
      question: 'Can I access classes on mobile?',
      answer:
        'Yes! The Sasthra platform is fully responsive and accessible on all devices, including desktops, tablets, and smartphones. Learn anytime, anywhere.'
    },
    {
      question: 'What kind of support can I expect?',
      answer:
        'We offer 24/7 support through our AI chat mentor for instant doubt resolution. Additionally, you’ll have access to scheduled sessions with human mentors for guidance, strategy, and in-depth problem-solving.'
    },
    {
      question: 'How is Sasthra different from other EdTech platforms?',
      answer:
        'Our core differentiator is the deep integration of our AI with expert human mentorship. We don’t just provide content; we provide a personalized and supportive ecosystem that adapts to you, ensuring your efforts are always focused on what matters most for your success.'
    }
  ];

  const navigation = [
    { name: 'About Us', href: '/aboutus' },
    { name: 'NEET', href: '/neet' },
    { name: 'JEE', href: '/jee' },
    { name: 'Events', href: '/upcomingevents' }
    // { name: 'Contact Us', href: '#' }
  ];

  const heroRef = useRef(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Define slide data
  const slides = [
    {
      image:
        'https://images.unsplash.com/photo-1541339907198-e08756dedf3f?q=80&w=2070&auto=format&fit=crop',
      title: (
        <>
          Empowering India's Future,
          <br />
          <span style={{ color: 'var(--color-counselor)' }}>
            One Student, One Unique Journey at a Time.
          </span>
        </>
      ),
      description:
        'At Sasthra, we harness AI to unlock the potential of every Indian student, bridging the gap between dreams and reality.',
      buttonText: 'Start Your Free Trial',
      buttonLink: '/auth'
    },
    {
      image: 'https://cdn.itm.ac.in/2024/05/tech-jobs-in-India--6--5.webp',
      title: (
        <>
          Ignite Your Academic Success,
          <br />
          <span style={{ color: 'var(--color-counselor)' }}>With Personalized AI Learning.</span>
        </>
      ),
      description:
        'Sasthra’s AI-driven platform tailors education to your unique needs, paving the way for JEE, NEET, and CBSE success.',
      buttonText: 'Explore Our Programs',
      buttonLink: '/programs'
    },
    {
      image:
        'https://t3.ftcdn.net/jpg/06/35/83/70/360_F_635837032_OCr4txNxjuMqe7K5xGa0YDPobfcytGBw.jpg',
      title: (
        <>
          Transform Your Future,
          <br />
          <span style={{ color: 'var(--color-counselor)' }}>With Sasthra’s Mentorship.</span>
        </>
      ),
      description:
        'Join Sasthra to receive expert guidance and mentorship, empowering you to achieve your academic and career goals.',
      buttonText: 'Join Now',
      buttonLink: '/join'
    }
  ];

  // Auto-scroll effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  // Parallax effect
  const { scrollYProgress: heroScroll } = useScroll({
    target: heroRef,
    offset: ['start start', 'end start']
  });
  const heroImageY = useTransform(heroScroll, [0, 1], ['0%', '40%']);
  const heroOpacity = useTransform(heroScroll, [0, 1], [1, 0]);

  // Animation variants for content and image
  const slideVariants = {
    initial: { x: '100%' },
    animate: { x: '0%', transition: { duration: 1, ease: [0.22, 1, 0.36, 1] } },
    exit: { x: '-100%', transition: { duration: 1, ease: [0.22, 1, 0.36, 1] } }
  };

  const contentVariants = {
    initial: { x: '100%', opacity: 0 },
    animate: { x: '0%', opacity: 1, transition: { duration: 0.8, ease: [0.22, 1, 0.36, 1] } },
    exit: { x: '-100%', opacity: 0, transition: { duration: 0.5 } }
  };

  const sectionRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ['start end', 'end start']
  });
  const yBg = useTransform(scrollYProgress, [0, 1], ['0%', '20%']);
  const yText = useTransform(scrollYProgress, [0, 1], ['0%', '30%']);
  const yCards = useTransform(scrollYProgress, [0, 1], ['0%', '15%']);
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0, 1, 0.5]);

  const missionRef = useRef(null);
  const { scrollYProgress: missionScroll } = useScroll({
    target: missionRef,
    offset: ['start end', 'end start']
  });
  const missionImageScale = useTransform(missionScroll, [0.1, 0.8], [1, 1.2]);
  const [activePanel, setActivePanel] = useState('student');
  const [activeFaqIndex, setActiveFaqIndex] = useState(0);

  // Sidebar animation variants
  const sidebarVariants = {
    open: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: '-100%',
      opacity: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <div className="bg-gray-50">
      <main>
        <section ref={heroRef} className="h-screen relative overflow-hidden">
          <AnimatePresence initial={false}>
            <motion.div
              key={currentSlide}
              className="absolute inset-0"
              style={{ y: heroImageY, opacity: heroOpacity }}
              variants={slideVariants}
              initial="initial"
              animate="animate"
              exit="exit">
              <img
                src={slides[currentSlide].image}
                alt="Hero background"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/40"></div>
            </motion.div>
          </AnimatePresence>

          {/* Header integrated into hero section */}
          <header className="absolute inset-x-0 top-0 z-50">
            <nav aria-label="Global" className="flex items-center justify-between p-6 lg:px-8">
              <div className="flex lg:flex-1">
                <a href="#" className="-m-1.5 p-1.5">
                  <span className="sr-only">Sasthra</span>
                  <img alt="Sasthra Logo" src={Logo} className="h-full w-40" />
                </a>
              </div>
              <div className="flex lg:hidden">
                <button
                  type="button"
                  onClick={() => setMobileMenuOpen(true)}
                  className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white">
                  <span className="sr-only">Open main menu</span>
                  <BarChart aria-hidden="true" className="size-6" />
                </button>
              </div>
              <div className="hidden lg:flex lg:gap-x-12">
                {navigation.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-sm/6 font-semibold mb-5 text-white hover:text-amber-400 transition-colors">
                    {item.name}
                  </a>
                ))}
              </div>
              <div className="hidden lg:flex lg:flex-1 lg:justify-end">
                <a
                  href="/auth"
                  className="text-sm/6 font-semibold mb-5 text-white hover:text-amber-400 transition-colors">
                  Log in <span aria-hidden="true">→</span>
                </a>
              </div>
            </nav>
          </header>

          {/* Mobile Sidebar */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.div
                className="fixed inset-0 z-50 lg:hidden"
                initial="closed"
                animate="open"
                exit="closed"
                variants={sidebarVariants}>
                <div
                  className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                  onClick={() => setMobileMenuOpen(false)}
                />
                <motion.div className="absolute top-0 left-0 w-4/5 max-w-sm h-full bg-gray-900 shadow-xl">
                  <div className="flex items-center justify-between p-6 border-b border-gray-700">
                    <img alt="Sasthra Logo" src={Logo} className="h-full w-32" />
                    <button
                      type="button"
                      onClick={() => setMobileMenuOpen(false)}
                      className="p-2 rounded-md text-white hover:bg-gray-800">
                      <span className="sr-only">Close menu</span>
                      <X className="size-6" aria-hidden="true" />
                    </button>
                  </div>
                  <nav className="flex flex-col p-6 space-y-4">
                    {navigation.map((item) => (
                      <a
                        key={item.name}
                        href={item.href}
                        className="text-white text-lg font-medium py-2 px-4 rounded-lg hover:bg-gray-800 hover:text-amber-400 transition-all flex items-center"
                        onClick={() => setMobileMenuOpen(false)}>
                        <ChevronRight className="w-5 h-5 mr-2 text-amber-500" />
                        {item.name}
                      </a>
                    ))}
                    <a
                      href="/auth"
                      className="text-white text-lg font-medium py-2 px-4 rounded-lg hover:bg-gray-800 hover:text-amber-400 transition-all flex items-center"
                      onClick={() => setMobileMenuOpen(false)}>
                      <ChevronRight className="w-5 h-5 mr-2 text-amber-500" />
                      Log in
                    </a>
                  </nav>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          <div className="relative z-10 h-full flex items-center justify-center">
            <div className="text-center text-white max-w-4xl mx-auto px-4">
              <AnimatePresence mode="wait" initial={false}>
                <motion.h1
                  key={`title-${currentSlide}`}
                  variants={contentVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  className="text-4xl md:text-6xl font-extrabold tracking-tight">
                  {slides[currentSlide].title}
                </motion.h1>
              </AnimatePresence>

              <AnimatePresence mode="wait" initial={false}>
                <motion.p
                  key={`description-${currentSlide}`}
                  variants={contentVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={{ delay: 0.2 }}
                  className="mt-6 text-lg md:text-xl max-w-2xl mx-auto text-gray-200">
                  {slides[currentSlide].description}
                </motion.p>
              </AnimatePresence>

              <AnimatePresence mode="wait" initial={false}>
                <motion.div
                  key={`button-${currentSlide}`}
                  variants={contentVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={{ delay: 0.4 }}
                  className="mt-10">
                  <a
                    href={slides[currentSlide].buttonLink}
                    className="px-8 py-3 font-semibold text-white rounded-lg transition-transform duration-300 transform hover:scale-105"
                    style={{
                      backgroundColor: 'var(--color-student)',
                      boxShadow: '0 10px 30px -10px var(--color-student)'
                    }}>
                    {slides[currentSlide].buttonText}
                  </a>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>

          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
            {slides.map((_, index) => (
              <motion.div
                key={index}
                className={`w-3 h-3 rounded-full ${index === currentSlide ? 'bg-[var(--color-counselor)]' : 'bg-white/50'}`}
                animate={{ scale: index === currentSlide ? 1.2 : 1 }}
                transition={{ duration: 0.3 }}
              />
            ))}
          </div>
        </section>

        <motion.section
          className="relative py-24 overflow-hidden bg-gradient-to-b from-slate-50 to-white"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}>
          <motion.div
            className="absolute left-0 top-0 w-64 h-64 opacity-5"
            style={{
              backgroundImage:
                "url('https://www.transparentpng.com/thumb/peacock-feather/peacock-feather-free-transparent-png-2.png')",
              backgroundSize: 'contain',
              filter: 'sepia(50%) hue-rotate(180deg)'
            }}
            animate={{
              rotate: [0, 15, 0],
              y: [0, 20, 0]
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />

          <div className="max-w-7xl mx-auto px-6 relative z-10">
            <div className="text-center mb-16">
              <motion.div
                className="inline-block mb-8"
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.6 }}>
                <div
                  className="w-16 h-16 rounded-full border-4 border-white shadow-md flex items-center justify-center"
                  style={{ backgroundColor: 'var(--color-teacher)' }}>
                  <BookOpenText size={28} className="text-white" />
                </div>
              </motion.div>

              <motion.h2
                className="text-4xl md:text-5xl font-bold text-black font-sans mb-6"
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6 }}>
                Empowering India's Next Generation
              </motion.h2>

              <motion.p
                className="text-lg text-gray-800 max-w-3xl mx-auto leading-relaxed"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.3 }}>
                Combining{' '}
                <span className="font-semibold" style={{ color: 'var(--color-teacher)' }}>
                  AI-powered personalization
                </span>{' '}
                with
                <span className="font-semibold" style={{ color: 'var(--color-student)' }}>
                  {' '}
                  India's finest educators
                </span>{' '}
                to create equal opportunities for all
              </motion.p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
              <motion.div
                className="bg-white p-6 rounded-xl shadow-lg"
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ type: 'spring', stiffness: 300 }}>
                <div className="flex items-center mb-6">
                  <img
                    src="https://images.unsplash.com/photo-1600180758890-6b94519a8ba6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                    alt="Student success story"
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-md mr-4"
                  />
                  <div>
                    <div className="text-3xl font-bold" style={{ color: 'var(--color-student)' }}>
                      10K+
                    </div>
                    <h3 className="text-lg font-semibold">Dreams Ignited</h3>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">
                  "The AI mentorship helped me crack JEE with AIR 1242 coming from a small village
                  school."
                </p>
                <div className="text-sm font-medium" style={{ color: 'var(--color-student)' }}>
                  Rahul, JEE Advanced
                </div>
              </motion.div>

              <motion.div
                className="bg-white p-6 rounded-xl shadow-lg"
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ type: 'spring', stiffness: 300, delay: 0.2 }}>
                <div className="flex items-center mb-6">
                  <img
                    src="https://t3.ftcdn.net/jpg/06/98/05/62/360_F_698056222_llz8ELIw40LcRm1RXPUsgsYbtUpwh8sx.jpg"
                    alt="Successful student"
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-md mr-4"
                  />
                  <div>
                    <div className="text-3xl font-bold" style={{ color: 'var(--color-counselor)' }}>
                      95%
                    </div>
                    <h3 className="text-lg font-semibold">Success Rate</h3>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">
                  "As a working professional, the 24/7 AI guidance was crucial for my NEET
                  preparation."
                </p>
                <div className="text-sm font-medium" style={{ color: 'var(--color-counselor)' }}>
                  Priya, NEET UG
                </div>
              </motion.div>

              <motion.div
                className="bg-white p-6 rounded-xl shadow-lg"
                initial={{ scale: 0.9, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                transition={{ type: 'spring', stiffness: 300, delay: 0.4 }}>
                <div className="flex items-center mb-6">
                  <img
                    src="https://t4.ftcdn.net/jpg/10/53/29/83/360_F_1053298337_n9cgQXd89gw94Y84BPXftIHJeTySUKCh.jpg"
                    alt="Mentor"
                    className="w-16 h-16 rounded-full object-cover border-4 border-white shadow-md mr-4"
                  />
                  <div>
                    <div className="text-3xl font-bold" style={{ color: 'var(--color-teacher)' }}>
                      24/7
                    </div>
                    <h3 className="text-lg font-semibold">AI Guidance</h3>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">
                  "The system adapts perfectly to each student's pace - like having a personal tutor
                  always available."
                </p>
                <div className="text-sm font-medium" style={{ color: 'var(--color-teacher)' }}>
                  Dr. Sharma, IIT Professor
                </div>
              </motion.div>
            </div>

            <motion.div
              className="bg-white rounded-2xl shadow-xl overflow-hidden"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
                <div className="p-10 flex flex-col justify-center">
                  <h3 className="text-4xl font-bold mb-4" style={{ color: 'var(--color-teacher)' }}>
                    Meet Our Educators
                  </h3>
                  <p className="text-gray-700 mb-6">
                    Our network of 500+ certified teachers and mentors from top institutions like
                    IITs, AIIMS, and NITs provide expert guidance alongside our AI system.
                  </p>
                  <div className="flex space-x-4">
                    {[1, 2, 3, 4].map((i) => (
                      <img
                        key={i}
                        src={`https://randomuser.me/api/portraits/${i % 2 === 0 ? 'women' : 'men'}/${i + 30}.jpg`}
                        alt="Educator"
                        className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                      />
                    ))}
                    <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium">
                      +500
                    </div>
                  </div>
                </div>
                <div className="h-full min-h-80 bg-gray-100 relative">
                  <img
                    src="https://img.freepik.com/premium-photo/indian-teachers-indian-students-indian-teachers-day_978786-46992.jpg?semt=ais_items_boosted&w=740"
                    alt="IIT Preparation"
                    className="absolute inset-0 w-full h-full object-cover object-center"
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                    <div className="text-white font-medium">
                      Live interactive session with IIT alumni
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.section>

        <section className="py-24 sm:py-32 bg-gradient-to-b from-slate-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto mb-20">
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-teacher)]">
                The Complete Indian Learning Experience
              </h2>
              <p className="mt-4 text-lg text-gray-600">
                Designed specifically for Indian students to excel in competitive exams through
                innovative technology
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
              <motion.div
                className="group relative hover:cursor-pointer overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                whileHover={{ y: -10 }}>
                <div className="absolute inset-0 bg-[var(--color-teacher)] opacity-90"></div>
                <div className="relative z-10 p-8 h-full flex flex-col">
                  <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mb-6">
                    <BrainCircuit size={28} className="text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Smart AI Learning</h3>
                  <p className="text-blue-100 mb-6">
                    Adaptive learning paths that evolve with your progress, focusing on your weak
                    areas while reinforcing strengths.
                  </p>
                  <div className="mt-auto">
                    <img
                      src="https://www.indiaspend.com/h-upload/2025/02/18/1500x900_1478516-digital-classroom-1500.webp"
                      alt="Indian student learning"
                      className="w-full h-48 object-cover rounded-lg opacity-90 group-hover:opacity-100 transition-opacity"
                    />
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="group relative hover:cursor-pointer overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                whileHover={{ y: -10 }}>
                <div className="absolute inset-0 bg-[var(--color-counselor)] opacity-90"></div>
                <div className="relative z-10 p-8 h-full flex flex-col">
                  <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mb-6">
                    <Video size={28} className="text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Expert Live Classes</h3>
                  <p className="text-amber-100 mb-6">
                    Interactive sessions with IIT/NIT alumni, bringing the best coaching institute
                    experience to your home.
                  </p>
                  <div className="mt-auto">
                    <img
                      src="https://img.freepik.com/premium-photo/student-with-laptop-having-online-class-home_380164-290437.jpg?semt=ais_items_boosted&w=740"
                      alt="Live class in progress"
                      className="w-full h-48 object-cover rounded-lg opacity-90 group-hover:opacity-100 transition-opacity"
                    />
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="group relative hover:cursor-pointer overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                whileHover={{ y: -10 }}>
                <div className="absolute inset-0 bg-[var(--color-teacher)] opacity-90"></div>
                <div className="relative z-10 p-8 h-full flex flex-col">
                  <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center mb-6">
                    <BarChart2 size={28} className="text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Advanced Analytics</h3>
                  <p className="text-emerald-100 mb-6">
                    Detailed performance tracking with AIR prediction and personalized improvement
                    recommendations.
                  </p>
                  <div className="mt-auto">
                    <img
                      src="https://i.pinimg.com/originals/2f/1a/8c/2f1a8c7d24f4d19e0a759ac8c2ff5778.jpg"
                      alt="Performance analytics"
                      className="w-full h-48 object-cover rounded-lg opacity-90 group-hover:opacity-100 transition-opacity"
                    />
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="mt-24 px-4">
              <div className="text-center mb-16">
                <h3 className="text-center mb-12 relative">
                  <span className="inline-block text-4xl font-bold text-gray-900 relative">
                    <span className="relative z-10 px-6">Voices of Excellence</span>
                    <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-amber-300 rounded-full"></span>
                  </span>
                  <span className="absolute -top-4 left-1/4 text-indigo-400">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 20.02L12 17.77L5.82 20.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                    </svg>
                  </span>
                  <span className="absolute -top-4 right-1/4 text-amber-400">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 20.02L12 17.77L5.82 20.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                    </svg>
                  </span>
                </h3>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  How India's brightest minds conquered competitive exams with our platform
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
                {[
                  {
                    quote:
                      'The vernacular explanations helped me overcome my English barrier and secure AIR 892 in JEE Advanced.',
                    name: 'Vikram Joshi',
                    achievement: 'IIT Delhi',
                    background: 'Repeater from Vijayawada',
                    image:
                      'https://images.unsplash.com/photo-1544717305-2782549b5136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80'
                  },
                  {
                    quote:
                      'From 520 to 680 marks in NEET - the test series and doubt-solving were game changers for me.',
                    name: 'Ananya Reddy',
                    achievement: 'AIIMS Delhi',
                    background: 'Hindi-medium student from Jaipur',
                    image:
                      'https://t3.ftcdn.net/jpg/07/93/11/70/360_F_793117099_fvcML3q6TarhP9gvIFIsK1cjFvzmvb4n.jpg'
                  },
                  {
                    quote:
                      'Managed 95% in CBSE and 98.6%ile in JEE with the integrated curriculum saving me 3 hours daily.',
                    name: 'Rohan Deshpande',
                    achievement: 'BITS Pilani',
                    background: 'Board exam student from Pune',
                    image:
                      'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80'
                  }
                ].map((testimonial, index) => (
                  <div key={index} className="relative group overflow-hidden rounded-2xl shadow-lg">
                    <div className="relative h-64 overflow-hidden">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent"></div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-white mx-4 mb-4 rounded-lg shadow-xl p-6 transform transition-all duration-300 group-hover:-translate-y-2">
                      <div className="flex items-start mb-4">
                        <svg
                          className="w-8 h-8 text-amber-500 mr-2 flex-shrink-0"
                          viewBox="0 0 24 24"
                          fill="currentColor">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                        </svg>
                        <p className="text-gray-700 italic">"{testimonial.quote}"</p>
                      </div>
                      <div className="border-t border-gray-100 pt-4">
                        <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                        <div className="flex flex-wrap gap-2 mt-2">
                          <span className="px-3 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">
                            {testimonial.achievement}
                          </span>
                          <span className="px-3 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">
                            {testimonial.background}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="absolute -top-4 -right-4 w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                      <svg
                        className="w-8 h-8 text-white"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor">
                        <path
                          d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 20.02L12 17.77L5.82 20.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-20 relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section ref={missionRef} className="h-[120vh] relative overflow-hidden">
          <div className="sticky top-0 h-screen flex items-center justify-center">
            <motion.div style={{ scale: missionImageScale }} className="absolute inset-0">
              <img
                src="https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=2070&auto=format&fit=crop"
                className="w-full h-full object-cover"
                alt="A mentor guiding a student"
              />
              <div className="absolute inset-0 bg-black/60"></div>
            </motion.div>
            <div className="relative z-10 max-w-4xl mx-auto text-center px-4">
              <div className="inline-block p-4 mb-4 bg-[var(--color-director)] rounded-2xl">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Our Mission</h2>
              <p className="text-xl md:text-2xl text-gray-200 font-medium leading-relaxed">
                To unlock the potential of every Indian student, from any economic background, to
                achieve success in all competitive exams by delivering personalized, engaging, and
                outcome-driven learning experiences.
              </p>
            </div>
          </div>
        </section>

        <section className="py-24 sm:py-32 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-teacher)]">
                Designed for Ambitious Achievers
              </h2>
              <p className="mt-4 text-lg text-gray-600">
                Whether you're a student aiming for the top or a parent supporting the journey,
                Sasthra is your dedicated partner. Hover over a card to learn more.
              </p>
            </div>
            <motion.div
              className="flex w-full flex-col lg:flex-row gap-6"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}>
              <SplitPanel
                title="For Aspiring Students"
                description="Get the foundational knowledge and advanced concepts needed to crack competitive exams with confidence."
                bgImage="https://media.gettyimages.com/id/1431046078/video/female-instructor-teaching-mature-adult-students-in-the-classroom.jpg?s=640x640&k=20&c=3D7bFBPG5pZIn8APWYoul7ZG0whtZ66Yo6hHV_Cw1gI="
                icon={<GraduationCap size={28} className="text-white" />}
                isActive={activePanel === 'student'}
                onHover={() => setActivePanel('student')}
              />
              <SplitPanel
                title="For Supportive Parents"
                description="Stay informed with transparent progress tracking and partner with us in your child's success story."
                bgImage="https://media.gettyimages.com/id/1334683017/photo/happy-father-and-son-using-laptop-at-home.jpg?s=612x612&w=0&k=20&c=hweCfoGe3v9eDyUlrWSJMkm7ULnLzeOKxn8ZFUEn6YU="
                icon={<Users size={28} className="text-white" />}
                isActive={activePanel === 'parent'}
                onHover={() => setActivePanel('parent')}
              />
            </motion.div>
          </div>
        </section>

        <section className="relative py-24 sm:py-32 bg-white overflow-hidden">
          <motion.div
            className="absolute top-0 left-0 w-full h-full opacity-10"
            style={{
              backgroundImage:
                "url('https://www.transparentpng.com/thumb/mandala/mandala-pattern-transparent-background-0.png')",
              backgroundSize: 'contain',
              backgroundPosition: 'center'
            }}
            initial={{ rotate: 0 }}
            whileInView={{ rotate: 360 }}
            transition={{ duration: 120, repeat: Infinity, ease: 'linear' }}
            viewport={{ once: true }}
          />

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              className="text-center max-w-3xl mx-auto mb-20"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}>
              <motion.div
                className="flex justify-center mb-6"
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}>
                <div className="flex space-x-2">
                  {['#f4c430', '#000080', '#f4c430', '#000080', '#f4c430'].map((color, i) => (
                    <motion.div
                      key={i}
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: color }}
                      animate={{
                        y: [0, -10, 0],
                        opacity: [0.6, 1, 0.6],
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    />
                  ))}
                </div>
              </motion.div>
              <div className="text-center max-w-3xl mx-auto mb-16 relative">
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-24 h-1 rounded-full"></div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="inline-block relative">
                  <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
                    <span className="relative inline-block">Answers for</span>
                  </h2>
                  <div className="relative inline-block mt-4">
                    <span className="text-4xl md:text-5xl font-bold relative z-10 px-4 py-2 bg-[var(--color-teacher)] text-white rounded-lg shadow-md">
                      Every Indian Learner
                    </span>
                    <div className="absolute -z-10 -inset-2 bg-black/10 rounded-xl blur-md"></div>
                  </div>
                </motion.div>
                <motion.p
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  viewport={{ once: true }}
                  className="text-lg text-gray-600 mt-8 relative">
                  <span className="absolute -left-0 top-1/2 transform -translate-y-1/2 text-[var(--color-teacher)] text-2xl">
                    “
                  </span>
                  Discover how our platform empowers students across India
                  <span className="absolute -right-0 top-1/2 transform -translate-y-1/2 text-[var(--color-teacher)] text-2xl">
                    ”
                  </span>
                </motion.p>
              </div>
            </motion.div>

            <div className="relative h-[110vh]">
              <div className="sticky top-0 h-screen flex items-center">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 w-full">
                  <motion.div
                    className="space-y-6"
                    initial={{ x: -50, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ staggerChildren: 0.1 }}
                    viewport={{ once: false, amount: 0.2 }}>
                    {faqData.slice(0, Math.ceil(faqData.length / 2)).map((faq, index) => (
                      <motion.div
                        key={index}
                        initial={{ y: 50, opacity: 0 }}
                        whileInView={{ y: 0, opacity: 1 }}
                        transition={{ type: 'spring', stiffness: 100 }}
                        viewport={{ once: false, margin: '-100px' }}>
                        <FaqItem
                          question={faq.question}
                          answer={faq.answer}
                          index={index}
                          activeFaqIndex={activeFaqIndex}
                          setActiveFaqIndex={setActiveFaqIndex}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                  <motion.div
                    className="hidden lg:block relative"
                    initial={{ opacity: 0, y: 100 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: false }}>
                    <motion.div
                      className="absolute inset-0 rounded-3xl overflow-hidden shadow-2xl"
                      style={{
                        backgroundImage:
                          "url('https://st4.depositphotos.com/16307542/31621/i/450/depositphotos_316215294-stock-photo-october-30-2019-kolkata-india.jpg')",
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                      initial={{ scale: 0.9, y: 100 }}
                      whileInView={{ scale: 1, y: 0 }}
                      transition={{
                        y: { type: 'spring', stiffness: 50 },
                        scale: { duration: 0.8 }
                      }}
                      viewport={{ once: false }}>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                      <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
                        <h3 className="text-2xl font-bold mb-2">Indian Education Revolution</h3>
                        <p>Personalized learning for every student</p>
                      </div>
                    </motion.div>
                  </motion.div>
                </div>
              </div>
            </div>

            <motion.div
              className="mt-20 rounded-3xl overflow-hidden relative bg-[var(--color-teacher)]"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ type: 'spring', damping: 15 }}
              viewport={{ once: true }}>
              <div className="absolute inset-0 opacity-10">
                <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                  <pattern
                    id="diagonalHatch"
                    width="10"
                    height="10"
                    patternTransform="rotate(45 0 0)"
                    patternUnits="userSpaceOnUse">
                    <line x1="0" y1="0" x2="0" y2="10" stroke="white" strokeWidth="1" />
                  </pattern>
                  <rect width="100%" height="100%" fill="url(#diagonalHatch)" />
                </svg>
              </div>
              <div className="relative grid lg:grid-cols-2 gap-8 p-8 lg:p-12 items-center">
                <div>
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight">
                    Need <span className="text-[var(--color-counselor)]">personalized</span>{' '}
                    guidance?
                  </h3>
                  <p className="text-amber-100 mb-8 text-lg">
                    Our Indian educators provide tailored solutions to help you achieve your goals.
                  </p>
                  <button className="px-8 py-4 bg-white text-amber-700 rounded-xl font-bold flex items-center gap-3 hover:bg-amber-100 transition-all">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                    Chat with Expert
                  </button>
                </div>
                <div className="hidden lg:block">
                  <motion.div
                    className="relative h-64"
                    initial={{ rotateY: 15 }}
                    whileInView={{ rotateY: 0 }}
                    transition={{ type: 'spring', stiffness: 100, damping: 15 }}
                    viewport={{ once: true }}>
                    <img
                      src="https://t3.ftcdn.net/jpg/11/84/41/32/360_F_1184413285_yack36xC6m3t9AqV5t5uCvjlZxttW9PC.jpg"
                      alt="Indian students learning"
                      className="absolute inset-0 w-full h-full object-cover rounded-2xl shadow-xl"
                    />
                    <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg">
                      <svg
                        className="w-12 h-12 text-[var(--color-counselor)]"
                        viewBox="0 0 24 24"
                        fill="currentColor">
                        <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 20.02L12 17.77L5.82 20.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                      </svg>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        <footer className="bg-gray-900 text-white py-16 relative overflow-hidden">
          <div className="absolute inset-0 opacity-20">
            <img
              src="https://www.toptal.com/designers/subtlepatterns/uploads/old_mathematics.png"
              alt="Indian mathematical pattern"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900/90"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-16">
              <div>
                <motion.div className="ml-[-40px]" whileHover={{ y: -3 }}>
                  <img src={Logo} alt="Sasthra Logo" />
                </motion.div>
                <p className="text-gray-300 mb-6">
                  Revolutionizing Indian education through AI-powered personalized learning
                  experiences.
                </p>
                <div className="flex space-x-4">
                  {[
                    { icon: <Twitter className="text-blue-400" />, label: 'Twitter' },
                    { icon: <Linkedin className="text-blue-600" />, label: 'LinkedIn' },
                    { icon: <Instagram className="text-pink-500" />, label: 'Instagram' },
                    { icon: <Youtube className="text-red-500" />, label: 'YouTube' }
                  ].map((social, i) => (
                    <motion.a
                      key={i}
                      href="#"
                      className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm"
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}>
                      {social.icon}
                    </motion.a>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-6 flex items-center">
                  <span className="w-2 h-6 bg-amber-500 rounded-full mr-2"></span>
                  Quick Links
                </h3>
                <ul className="space-y-3">
                  {[
                    { name: 'About Us', href: '/aboutus' },
                    { name: 'NEET', href: '/neet' },
                    { name: 'JEE', href: '/jee' },
                    { name: 'SignIn', href: '/auth' }
                  ].map((item, i) => (
                    <motion.li key={i} whileHover={{ x: 5 }}>
                      <a
                        href={item.href}
                        className="text-gray-300 hover:text-amber-400 flex items-center">
                        <ChevronRight className="w-4 h-4 mr-2 text-amber-500" />
                        {item.name}
                      </a>
                    </motion.li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-6 flex items-center">
                  <span className="w-2 h-6 bg-amber-500 rounded-full mr-2"></span>
                  Coaching Resources
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {['JEE Main', 'JEE Advanced', 'NEET UG', 'Board Exams', 'Sasthra Plus'].map(
                    (exam, i) => (
                      <motion.a
                        key={i}
                        href="#"
                        className="px-3 py-2 bg-white/5 hover:bg-amber-500/20 rounded-lg text-sm flex items-center border border-white/10 hover:border-amber-500/30"
                        whileHover={{ y: -2 }}>
                        <Bookmark className="w-4 h-4 mr-2 text-amber-500" />
                        {exam}
                      </motion.a>
                    )
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-6 flex items-center">
                  <span className="w-2 h-6 bg-amber-500 rounded-full mr-2"></span>
                  Contact Sasthra
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <MapPin className="w-5 h-5 mt-1 text-amber-500 mr-3" />
                    <p className="text-gray-300">
                      12, Supreme Residency,
                      <br />
                      Old Rajeev Gandhi Nagar,
                      <br />
                      Kota, Rajasthan - 324005,
                      <br />
                      India
                    </p>
                  </div>
                  <div className="flex items-center">
                    <Phone className="w-5 h-5 text-amber-500 mr-3" />
                    <a href="tel:+************" className="text-gray-300 hover:text-white">
                      9198333879
                    </a>
                  </div>
                  <div className="flex items-center">
                    <Mail className="w-5 h-5 text-amber-500 mr-3" />
                    <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div className="border-t border-gray-700 pt-8">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <p className="text-gray-400 text-sm mb-4 md:mb-0">
                  Sasthra EdTech. All rights reserved.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  {['Privacy Policy', 'Terms of Service'].map((item, i) => (
                    <motion.a
                      key={i}
                      href="#"
                      className="text-sm text-gray-400 hover:text-amber-400"
                      whileHover={{ y: -1 }}>
                      {item}
                    </motion.a>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  );
};

export default LandPage;
